/**
 * Blur detection utility using Laplacian variance
 * 
 * This utility provides functions to detect blur in images using the Laplacian variance method.
 * The Laplacian operator is used to detect edges in an image, and the variance of the result
 * indicates how much edge information is present. Lower variance typically indicates more blur.
 */

/**
 * Represents image data with dimensions
 */
export interface ImageData {
  data: Uint8Array; // Pixel data in RGBA format
  width: number;
  height: number;
}

/**
 * Result of blur detection analysis
 */
export interface BlurDetectionResult {
  laplacianVariance: number;
  isBlurry: boolean;
  blurScore: number; // Normalized score between 0-1 (0 = very blurry, 1 = very sharp)
}

/**
 * Configuration for blur detection
 */
export interface BlurDetectionConfig {
  threshold: number; // Threshold below which image is considered blurry
  normalizeScore: boolean; // Whether to normalize the score to 0-1 range
}

/**
 * Default configuration for blur detection
 */
export const DEFAULT_BLUR_CONFIG: BlurDetectionConfig = {
  threshold: 100, // This will need tuning based on testing
  normalizeScore: true,
};

/**
 * Calculate Laplacian variance for blur detection
 * 
 * @param imageData - Image data with pixel information
 * @param config - Configuration options for blur detection
 * @returns Blur detection result with variance and blur assessment
 */
export function calculateLaplacianVariance(
  imageData: ImageData,
  config: BlurDetectionConfig = DEFAULT_BLUR_CONFIG
): BlurDetectionResult {
  const { data, width, height } = imageData;
  
  // Convert RGBA to grayscale first
  const grayscale = convertToGrayscale(data, width, height);
  
  // Apply Laplacian operator
  const laplacian = applyLaplacianOperator(grayscale, width, height);
  
  // Calculate variance of the Laplacian
  const variance = calculateVariance(laplacian);
  
  // Determine if image is blurry based on threshold
  const isBlurry = variance < config.threshold;
  
  // Calculate normalized blur score if requested
  let blurScore = variance;
  if (config.normalizeScore) {
    // Simple normalization - this may need adjustment based on testing
    blurScore = Math.min(variance / 1000, 1); // Assuming max variance around 1000
  }
  
  return {
    laplacianVariance: variance,
    isBlurry,
    blurScore,
  };
}

/**
 * Convert RGBA image data to grayscale
 * 
 * @param data - RGBA pixel data
 * @param width - Image width
 * @param height - Image height
 * @returns Grayscale pixel array
 */
function convertToGrayscale(data: Uint8Array, width: number, height: number): number[] {
  const grayscale: number[] = [];
  
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    // Using standard luminance formula
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
    grayscale.push(gray);
  }
  
  return grayscale;
}

/**
 * Apply Laplacian operator to grayscale image
 * 
 * @param grayscale - Grayscale pixel data
 * @param width - Image width
 * @param height - Image height
 * @returns Laplacian filtered image
 */
function applyLaplacianOperator(grayscale: number[], width: number, height: number): number[] {
  const result: number[] = [];
  
  // Laplacian kernel (8-connected)
  const kernel = [
    [0, -1, 0],
    [-1, 4, -1],
    [0, -1, 0]
  ];
  
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let sum = 0;
      
      // Apply kernel
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const pixelIndex = (y + ky) * width + (x + kx);
          sum += grayscale[pixelIndex] * kernel[ky + 1][kx + 1];
        }
      }
      
      result.push(Math.abs(sum));
    }
  }
  
  return result;
}

/**
 * Calculate variance of an array of numbers
 * 
 * @param values - Array of numeric values
 * @returns Variance of the values
 */
function calculateVariance(values: number[]): number {
  if (values.length === 0) return 0;
  
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  const variance = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  
  return variance;
}
